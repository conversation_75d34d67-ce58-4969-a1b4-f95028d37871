import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../constants/app_tabs.dart';
import '../controllers/transaction_controller.dart';
import '../tabs/transaction_analytics_tab.dart';
import '../tabs/transaction_records_tab.dart';
import '../tabs/transaction_insights_tab.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart'; // Import Universal Components

/// Transactions screen with Provider-managed controller lifecycle
/// Following the WeightScreen pattern: StatelessWidget with ChangeNotifierProvider
class TransactionsScreen extends StatelessWidget {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => TransactionController(),
      child: const _TransactionsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _TransactionsScreenContent extends StatefulWidget {
  const _TransactionsScreenContent();

  @override
  State<_TransactionsScreenContent> createState() => _TransactionsScreenContentState();
}

class _TransactionsScreenContentState extends State<_TransactionsScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab1Color: UniversalEmptyStateTheme.transactions, // Blue for analytics
      tab2Label: 'Records',
      tab2Icon: Icons.list,
      tab2Color: const Color(0xFF388E3C), // Green for records
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
      tab3Color: Colors.purple, // Purple for insights
    );

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB when tab changes
    _tabController.addListener(() {
      if (mounted) {
        safeSetState(() {}); // Use safe setState from UniversalScreenState
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction Management'),
        backgroundColor: UniversalEmptyStateTheme.transactions,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.transactionsReport,
            ),
            tooltip: 'View Transaction Reports',
          ),
          // Remove refresh button - reactive streams handle updates automatically
          Consumer<TransactionController>(
            builder: (context, controller, child) {
              return IconButton(
                icon: Icon(controller.state == ControllerState.loading ? Icons.hourglass_empty : Icons.analytics),
                onPressed: null, // No manual refresh needed
                tooltip: controller.state == ControllerState.loading ? 'Loading...' : 'Real-time data',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.transactions,
          ),
          // Tab Views with reactive state management
          Expanded(
            child: Consumer<TransactionController>(
              builder: (context, controller, child) {
                return UniversalStateBuilder(
                  state: _getScreenStateFromController(controller),
                  errorMessage: controller.errorMessage,
                  onRetry: () => controller.refresh(),
                  moduleColor: UniversalEmptyStateTheme.transactions,
                  loadingWidget: UniversalLoadingIndicator.transactions(),
                  errorWidget: UniversalErrorIndicator.transactions(
                    message: controller.errorMessage ?? 'Failed to load transaction data',
                    onRetry: () => controller.refresh(),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      TransactionAnalyticsTab(controller: controller),
                      TransactionRecordsTab(controller: controller),
                      TransactionInsightsTab(controller: controller),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _buildFloatingActionButton() ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab
        return Consumer<TransactionController>(
          builder: (context, controller, child) {
            return UniversalFAB.add(
              onPressed: () => _addTransaction(controller),
              tooltip: 'Add Transaction',
            );
          },
        );
      default:
        return null;
    }
  }

  void _addTransaction(TransactionController controller) {
    showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: controller.categories,
        // No onTransactionAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(TransactionController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
