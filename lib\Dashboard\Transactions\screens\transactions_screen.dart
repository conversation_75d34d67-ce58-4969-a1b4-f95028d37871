import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/transaction_controller.dart';
import '../tabs/transaction_analytics_tab.dart';
import '../tabs/transaction_records_tab.dart';
import '../tabs/transaction_insights_tab.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Transactions screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class TransactionsScreen extends StatelessWidget {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => TransactionController(),
      child: const _TransactionsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _TransactionsScreenContent extends StatefulWidget {
  const _TransactionsScreenContent();

  @override
  State<_TransactionsScreenContent> createState() => _TransactionsScreenContentState();
}

class _TransactionsScreenContentState extends State<_TransactionsScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Transaction Management',
      body: Consumer<TransactionController>(
        builder: (context, transactionController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => TransactionAnalyticsTab(controller: transactionController),
              ),
              Builder(
                builder: (context) => TransactionRecordsTab(controller: transactionController),
              ),
              Builder(
                builder: (context) => TransactionInsightsTab(controller: transactionController),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.insights],
            colors: [
              UniversalEmptyStateTheme.transactions, // Blue for analytics
              const Color(0xFF388E3C), // Green for records
              Colors.purple, // Purple for insights
            ],
            showFABs: const [false, true, false], // FAB only on Records tab
            indicatorColor: UniversalEmptyStateTheme.transactions,
          );

          return UniversalStateBuilder(
            state: _getScreenStateFromController(transactionController),
            errorMessage: transactionController.errorMessage,
            onRetry: () => executeWithLoading(() => transactionController.refresh()),
            moduleColor: UniversalEmptyStateTheme.transactions,
            loadingWidget: UniversalLoadingIndicator.transactions(),
            errorWidget: UniversalErrorIndicator.transactions(
              message: transactionController.errorMessage ?? 'Failed to load transaction data',
              onRetry: () => executeWithLoading(() => transactionController.refresh()),
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.transactionsReport,
          ),
          tooltip: 'View Transaction Reports',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => executeWithLoading(() => context.read<TransactionController>().refresh()),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _addTransaction,
            tooltip: 'Add Transaction',
            backgroundColor: UniversalEmptyStateTheme.transactions,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      onRefresh: () => executeWithLoading(() => context.read<TransactionController>().refresh()),
    );
  }

  void _addTransaction() {
    final transactionController = context.read<TransactionController>();

    showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: transactionController.categories,
        // No onTransactionAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(TransactionController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
