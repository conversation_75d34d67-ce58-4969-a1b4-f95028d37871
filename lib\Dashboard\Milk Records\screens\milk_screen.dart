import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../constants/app_tabs.dart';
import '../controllers/milk_controller.dart';
import '../tabs/milk_analytics_tab.dart';
import '../tabs/milk_records_tab.dart';
import '../tabs/milk_sales_tab.dart';
import '../tabs/milk_insights_tab.dart';
import '../dialogs/milk_record_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart'; // Import Universal Components

/// Milk screen with Provider-managed controller lifecycle
/// Following the WeightScreen pattern: StatelessWidget with ChangeNotifierProvider
class MilkScreen extends StatelessWidget {
  const MilkScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => MilkController(),
      child: const _MilkScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _MilkScreenContent extends StatefulWidget {
  const _MilkScreenContent();

  @override
  State<_MilkScreenContent> createState() => _MilkScreenContentState();
}

class _MilkScreenContentState extends State<_MilkScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = [
      TabItem(icon: Icons.analytics, label: 'Analytics', color: UniversalEmptyStateTheme.milk, screen: Container()),
      TabItem(icon: Icons.list_alt, label: 'Records', color: UniversalEmptyStateTheme.milk, screen: Container()),
      TabItem(icon: Icons.point_of_sale, label: 'Sales', color: UniversalEmptyStateTheme.milk, screen: Container()),
      TabItem(icon: Icons.lightbulb, label: 'Insights', color: UniversalEmptyStateTheme.milk, screen: Container()),
    ];

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB when tab changes
    _tabController.addListener(() {
      if (mounted) {
        safeSetState(() {}); // Use safe setState from UniversalScreenState
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Management'),
        backgroundColor: UniversalEmptyStateTheme.milk,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.milkReport,
            ),
            tooltip: 'View Milk Reports',
          ),
          // Remove refresh button - reactive streams handle updates automatically
          Consumer<MilkController>(
            builder: (context, controller, child) {
              return IconButton(
                icon: Icon(controller.state == ControllerState.loading ? Icons.hourglass_empty : Icons.analytics),
                onPressed: null, // No manual refresh needed
                tooltip: controller.state == ControllerState.loading ? 'Loading...' : 'Real-time data',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.milk,
          ),
          // Tab Views with reactive state management
          Expanded(
            child: Consumer<MilkController>(
              builder: (context, controller, child) {
                return UniversalStateBuilder(
                  state: _getScreenStateFromController(controller),
                  errorMessage: controller.errorMessage,
                  onRetry: () => controller.refresh(),
                  moduleColor: UniversalEmptyStateTheme.milk,
                  loadingWidget: UniversalLoadingIndicator.milk(),
                  errorWidget: UniversalErrorIndicator.milk(
                    message: controller.errorMessage ?? 'Failed to load milk data',
                    onRetry: () => controller.refresh(),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      MilkAnalyticsTab(controller: controller),
                      MilkRecordsTab(controller: controller),
                      MilkSalesTab(controller: controller),
                      MilkInsightsTab(controller: controller),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _buildFloatingActionButton() ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab
      case 2: // Sales tab
        return Consumer<MilkController>(
          builder: (context, controller, child) {
            return UniversalFAB.add(
              onPressed: () => _showAddDialog(controller),
              tooltip: 'Add Record',
            );
          },
        );
      default:
        return null;
    }
  }

  void _showAddDialog(MilkController controller) {
    // Show appropriate dialog based on current tab
    switch (_tabController.index) {
      case 1: // Milk Records
        showDialog(
          context: context,
          builder: (context) => MilkRecordFormDialog(
            cattle: controller.cattle,
            // No onRecordAdded callback needed - reactive streams handle updates!
          ),
        );
        break;
      case 2: // Milk Sales
        // Add milk sale dialog when it exists
        break;
    }
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(MilkController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}