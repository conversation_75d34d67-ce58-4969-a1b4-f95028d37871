import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';

import '../controllers/cattle_controller.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../tabs/cattle_records_tab.dart';
import '../tabs/cattle_analytics_tab.dart';
import '../tabs/cattle_insights_tab.dart';
import '../services/cattle_insights_service.dart';
import '../../../utils/message_utils.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

class CattleScreen extends StatefulWidget {
  const CattleScreen({Key? key}) : super(key: key);

  @override
  State<CattleScreen> createState() => _CattleScreenState();
}

class _CattleScreenState extends State<CattleScreen>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  UniversalTabManager? _tabManager;



  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 3, vsync: this);
    // Remove setState() listener - FAB will use AnimatedBuilder instead
  }



  @override
  void dispose() {
    _tabController.dispose();
    // Controller disposal is handled by Provider
    super.dispose();
  }


  void _showAddCattleDialog() {
    final cattleController = context.read<CattleController>();

    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        cattle: null,
        businessId: cattleController.businessId,
        animalTypes: cattleController.animalTypes,
        onSave: (newCattle) async {
          try {
            await cattleController.addCattle(newCattle);

            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              CattleMessageUtils.showSuccess(context,
                  CattleMessageUtils.cattleRecordCreated());
            }

          } catch (e) {
            debugPrint('Error adding cattle: $e');

            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              CattleMessageUtils.showError(context, 'Error adding cattle');
            }
          }
        },
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Cattle Management',
      body: Consumer<CattleController>(
        builder: (context, cattleController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => CattleAnalyticsTab(controller: cattleController),
              ),
              Builder(
                builder: (context) => const CattleRecordsTab(), // Uses Provider pattern
              ),
              Builder(
                builder: (context) => CattleInsightsTab(
                  // Ultimate Pure Dependency Injection: ALL dependencies provided by parent
                  // Widget has ZERO knowledge of dependency creation - perfect architectural purity
                  controller: cattleController,
                  insightsService: GetIt.instance<CattleInsightsService>(),
                ),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.lightbulb],
            colors: [
              AppColors.cattleKpiColors[0], // Blue
              AppColors.cattleKpiColors[1], // Green
              AppColors.cattleKpiColors[2], // Purple
            ],
            showFABs: const [false, true, false], // FAB only on Records tab
            indicatorColor: AppColors.cattleHeader,
          );

          return UniversalStateBuilder(
            state: _getScreenStateFromController(cattleController),
            errorMessage: cattleController.errorMessage,
            onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            moduleColor: UniversalEmptyStateTheme.cattle,
            loadingWidget: UniversalLoadingIndicator.cattle(),
            errorWidget: UniversalErrorIndicator.cattle(
              message: cattleController.errorMessage ?? 'Failed to load cattle data',
              onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.cattleReport,
          ),
          tooltip: 'View Cattle Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _showAddCattleDialog,
            tooltip: 'Add Cattle',
            backgroundColor: AppColors.primary,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(CattleController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
