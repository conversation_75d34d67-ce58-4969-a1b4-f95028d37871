import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../constants/app_tabs.dart';
import '../controllers/events_controller.dart';
import '../tabs/events_analytics_tab.dart';
import '../tabs/events_records_tab.dart';
import '../tabs/events_insights_tab.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart'; // Import Universal Components

/// Events screen with Provider-managed controller lifecycle
/// Following the WeightScreen pattern: StatelessWidget with ChangeNotifierProvider
class EventsScreen extends StatelessWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => EventsController(),
      child: const _EventsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _EventsScreenContent extends StatefulWidget {
  const _EventsScreenContent();

  @override
  State<_EventsScreenContent> createState() => _EventsScreenContentState();
}

class _EventsScreenContentState extends State<_EventsScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab1Color: UniversalEmptyStateTheme.events, // Blue for analytics
      tab2Label: 'Records',
      tab2Icon: Icons.event,
      tab2Color: const Color(0xFF388E3C), // Green for records
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
      tab3Color: Colors.purple, // Purple for insights
    );

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB when tab changes
    _tabController.addListener(() {
      if (mounted) {
        safeSetState(() {}); // Use safe setState from UniversalScreenState
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events Management'),
        backgroundColor: UniversalEmptyStateTheme.events,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.eventsReport,
            ),
            tooltip: 'View Events Reports',
          ),
          // Remove refresh button - reactive streams handle updates automatically
          Consumer<EventsController>(
            builder: (context, controller, child) {
              return IconButton(
                icon: Icon(controller.state == ControllerState.loading ? Icons.hourglass_empty : Icons.analytics),
                onPressed: null, // No manual refresh needed
                tooltip: controller.state == ControllerState.loading ? 'Loading...' : 'Real-time data',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.events,
          ),
          // Tab Views with reactive state management
          Expanded(
            child: Consumer<EventsController>(
              builder: (context, controller, child) {
                return UniversalStateBuilder(
                  state: _getScreenStateFromController(controller),
                  errorMessage: controller.errorMessage,
                  onRetry: () => controller.refresh(),
                  moduleColor: UniversalEmptyStateTheme.events,
                  loadingWidget: UniversalLoadingIndicator.events(),
                  errorWidget: UniversalErrorIndicator.events(
                    message: controller.errorMessage ?? 'Failed to load events data',
                    onRetry: () => controller.refresh(),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      EventsAnalyticsTab(controller: controller),
                      EventsRecordsTab(controller: controller),
                      EventsInsightsTab(controller: controller),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _buildFloatingActionButton() ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab
        return Consumer<EventsController>(
          builder: (context, controller, child) {
            return UniversalFAB.add(
              onPressed: () => _addEvent(controller),
              tooltip: 'Add Event',
            );
          },
        );
      default:
        return null;
    }
  }

  void _addEvent(EventsController controller) {
    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattle: controller.cattle,
        eventTypes: controller.eventTypes,
        // No onRecordAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(EventsController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
