import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/events_controller.dart';
import '../tabs/events_analytics_tab.dart';
import '../tabs/events_records_tab.dart';
import '../tabs/events_insights_tab.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../widgets/mixins/screen_state_mapper.dart'; // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Events screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class EventsScreen extends StatelessWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => EventsController(),
      child: const _EventsScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _EventsScreenContent extends StatefulWidget {
  const _EventsScreenContent();

  @override
  State<_EventsScreenContent> createState() => _EventsScreenContentState();
}

class _EventsScreenContentState extends State<_EventsScreenContent>
    with TickerProviderStateMixin, UniversalScreenState, ScreenStateMapper {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Events Management',
      body: Consumer<EventsController>(
        builder: (context, eventsController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => EventsAnalyticsTab(controller: eventsController),
              ),
              Builder(
                builder: (context) => EventsRecordsTab(controller: eventsController),
              ),
              Builder(
                builder: (context) => EventsInsightsTab(controller: eventsController),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Insights'],
            icons: const [Icons.analytics, Icons.event, Icons.insights],
            colors: [
              UniversalEmptyStateTheme.events, // Teal for analytics
              const Color(0xFF388E3C), // Green for records
              Colors.purple, // Purple for insights
            ],
            showFABs: const [false, true, false], // FAB only on Records tab
            indicatorColor: UniversalEmptyStateTheme.events,
          );

          return UniversalStateBuilder(
            state: getScreenStateFromController(eventsController),
            errorMessage: eventsController.errorMessage,
            onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            moduleColor: UniversalEmptyStateTheme.events,
            loadingWidget: UniversalLoadingIndicator.events(),
            errorWidget: UniversalErrorIndicator.events(
              message: eventsController.errorMessage ?? 'Failed to load events data',
              onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.eventsReport,
          ),
          tooltip: 'View Events Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _addEvent,
            tooltip: 'Add Event',
            backgroundColor: UniversalEmptyStateTheme.events,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  void _addEvent() {
    final eventsController = context.read<EventsController>();

    showDialog(
      context: context,
      builder: (context) => EventFormDialog(
        cattle: eventsController.cattle,
        eventTypes: eventsController.eventTypes,
        // No onRecordAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}
