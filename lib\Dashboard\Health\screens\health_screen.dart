import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/health_controller.dart';
import '../tabs/health_analytics_tab.dart';
import '../tabs/health_records_tab.dart';
import '../tabs/treatments_tab.dart';
import '../tabs/vaccinations_tab.dart';
import '../tabs/health_insights_tab.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Health screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class HealthScreen extends StatelessWidget {
  const HealthScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HealthController(),
      child: const _HealthScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _HealthScreenContent extends StatefulWidget {
  const _HealthScreenContent();

  @override
  State<_HealthScreenContent> createState() => _HealthScreenContentState();
}

class _HealthScreenContentState extends State<_HealthScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddHealthDialog() {
    final healthController = context.read<HealthController>();

    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        cattle: healthController.cattle,
        // No onRecordAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Health Management',
      body: Consumer<HealthController>(
        builder: (context, healthController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.fiveTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => HealthAnalyticsTab(controller: healthController),
              ),
              Builder(
                builder: (context) => HealthRecordsTab(controller: healthController),
              ),
              Builder(
                builder: (context) => TreatmentsTab(controller: healthController),
              ),
              Builder(
                builder: (context) => VaccinationsTab(controller: healthController),
              ),
              Builder(
                builder: (context) => HealthInsightsTab(controller: healthController),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Treatments', 'Vaccinations', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.healing, Icons.vaccines, Icons.lightbulb],
            colors: [
              UniversalEmptyStateTheme.health, // Red for analytics
              UniversalEmptyStateTheme.health, // Red for records
              UniversalEmptyStateTheme.health, // Red for treatments
              UniversalEmptyStateTheme.health, // Red for vaccinations
              UniversalEmptyStateTheme.health, // Red for insights
            ],
            showFABs: const [false, true, true, true, false], // FAB on Records, Treatments, Vaccinations tabs
            indicatorColor: UniversalEmptyStateTheme.health,
          );

          return UniversalStateBuilder(
            state: _getScreenStateFromController(healthController),
            errorMessage: healthController.errorMessage,
            onRetry: () => executeWithLoading(() => healthController.refresh()),
            moduleColor: UniversalEmptyStateTheme.health,
            loadingWidget: UniversalLoadingIndicator.health(),
            errorWidget: UniversalErrorIndicator.health(
              message: healthController.errorMessage ?? 'Failed to load health data',
              onRetry: () => executeWithLoading(() => healthController.refresh()),
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.healthReport,
          ),
          tooltip: 'View Health Reports',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => executeWithLoading(() => context.read<HealthController>().refresh()),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _showAddHealthDialog,
            tooltip: 'Add Record',
            backgroundColor: UniversalEmptyStateTheme.health,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      onRefresh: () => executeWithLoading(() => context.read<HealthController>().refresh()),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(HealthController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
