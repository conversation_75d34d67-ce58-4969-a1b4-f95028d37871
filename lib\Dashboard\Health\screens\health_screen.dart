import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../constants/app_tabs.dart';
import '../controllers/health_controller.dart';
import '../tabs/health_analytics_tab.dart';
import '../tabs/health_records_tab.dart';
import '../tabs/treatments_tab.dart';
import '../tabs/vaccinations_tab.dart';
import '../tabs/health_insights_tab.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart'; // Import Universal Components

/// Health screen with Provider-managed controller lifecycle
/// Following the WeightScreen pattern: StatelessWidget with ChangeNotifierProvider
class HealthScreen extends StatelessWidget {
  const HealthScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => HealthController(),
      child: const _HealthScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _HealthScreenContent extends StatefulWidget {
  const _HealthScreenContent();

  @override
  State<_HealthScreenContent> createState() => _HealthScreenContentState();
}

class _HealthScreenContentState extends State<_HealthScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = [
      TabItem(icon: Icons.analytics, label: 'Analytics', color: UniversalEmptyStateTheme.health, screen: Container()),
      TabItem(icon: Icons.list, label: 'Records', color: UniversalEmptyStateTheme.health, screen: Container()),
      TabItem(icon: Icons.healing, label: 'Treatments', color: UniversalEmptyStateTheme.health, screen: Container()),
      TabItem(icon: Icons.vaccines, label: 'Vaccinations', color: UniversalEmptyStateTheme.health, screen: Container()),
      TabItem(icon: Icons.lightbulb, label: 'Insights', color: UniversalEmptyStateTheme.health, screen: Container()),
    ];

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB when tab changes
    _tabController.addListener(() {
      if (mounted) {
        safeSetState(() {}); // Use safe setState from UniversalScreenState
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health Management'),
        backgroundColor: UniversalEmptyStateTheme.health,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.healthReport,
            ),
            tooltip: 'View Health Reports',
          ),
          // Remove refresh button - reactive streams handle updates automatically
          Consumer<HealthController>(
            builder: (context, controller, child) {
              return IconButton(
                icon: Icon(controller.state == ControllerState.loading ? Icons.hourglass_empty : Icons.analytics),
                onPressed: null, // No manual refresh needed
                tooltip: controller.state == ControllerState.loading ? 'Loading...' : 'Real-time data',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.health,
          ),
          // Tab Views with reactive state management
          Expanded(
            child: Consumer<HealthController>(
              builder: (context, controller, child) {
                return UniversalStateBuilder(
                  state: _getScreenStateFromController(controller),
                  errorMessage: controller.errorMessage,
                  onRetry: () => controller.refresh(),
                  moduleColor: UniversalEmptyStateTheme.health,
                  loadingWidget: UniversalLoadingIndicator.health(),
                  errorWidget: UniversalErrorIndicator.health(
                    message: controller.errorMessage ?? 'Failed to load health data',
                    onRetry: () => controller.refresh(),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      HealthAnalyticsTab(controller: controller),
                      HealthRecordsTab(controller: controller),
                      TreatmentsTab(controller: controller),
                      VaccinationsTab(controller: controller),
                      HealthInsightsTab(controller: controller),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _buildFloatingActionButton() ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab
      case 2: // Treatments tab
      case 3: // Vaccinations tab
        return Consumer<HealthController>(
          builder: (context, controller, child) {
            return UniversalFAB.add(
              onPressed: () => _showAddDialog(controller),
              tooltip: 'Add Record',
            );
          },
        );
      default:
        return null;
    }
  }

  void _showAddDialog(HealthController controller) {
    // Show appropriate dialog based on current tab
    switch (_tabController.index) {
      case 1: // Health Records
        showDialog(
          context: context,
          builder: (context) => HealthRecordFormDialog(
            cattle: controller.cattle,
            // No onRecordAdded callback needed - reactive streams handle updates!
          ),
        );
        break;
      // Add cases for treatment and vaccination dialogs when they exist
    }
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(HealthController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
