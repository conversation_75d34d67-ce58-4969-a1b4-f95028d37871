import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/weight_controller.dart';
import '../dialogs/weight_form_dialog.dart';
import '../tabs/weight_records_tab.dart';
import '../tabs/weight_analytics_tab.dart';
import '../tabs/weight_insights_tab.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

/// Weight screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class WeightScreen extends StatelessWidget {
  const WeightScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => WeightController(),
      child: const _WeightScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _WeightScreenContent extends StatefulWidget {
  const _WeightScreenContent();

  @override
  State<_WeightScreenContent> createState() => _WeightScreenContentState();
}

class _WeightScreenContentState extends State<_WeightScreenContent>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddWeightDialog() {
    final weightController = context.read<WeightController>();

    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: weightController.allCattle,
        // No onRecordAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Weight Management',
      body: Consumer<WeightController>(
        builder: (context, weightController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.threeTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => WeightAnalyticsTab(controller: weightController),
              ),
              Builder(
                builder: (context) => WeightRecordsTab(controller: weightController),
              ),
              Builder(
                builder: (context) => WeightInsightsTab(controller: weightController),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Insights'],
            icons: const [Icons.analytics, Icons.list, Icons.lightbulb],
            colors: [
              Colors.blue, // Blue for analytics
              const Color(0xFF2E7D32), // Green for records
              Colors.purple, // Purple for insights
            ],
            showFABs: const [false, true, false], // FAB only on Records tab
            indicatorColor: const Color(0xFF2E7D32),
          );

          return UniversalStateBuilder(
            state: _getScreenStateFromController(weightController),
            errorMessage: weightController.error,
            onRetry: () => executeWithLoading(() => weightController.refresh()),
            moduleColor: const Color(0xFF2E7D32),
            loadingWidget: const Center(child: CircularProgressIndicator()),
            errorWidget: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    weightController.error ?? 'Failed to load weight data',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.weightReport,
          ),
          tooltip: 'View Weight Reports',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => executeWithLoading(() => context.read<WeightController>().refresh()),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _showAddWeightDialog,
            tooltip: 'Add Weight Record',
            backgroundColor: const Color(0xFF2E7D32),
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      onRefresh: () => executeWithLoading(() => context.read<WeightController>().refresh()),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(WeightController controller) {
    if (controller.isLoading) return ScreenState.loading;
    if (controller.error != null) return ScreenState.error;
    return ScreenState.loaded;
  }
}